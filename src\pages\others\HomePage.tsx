import { Image, Space, Typography } from "antd"
import { ServicesGrid } from "~/components"
import { createStyles } from "~/themes"

const { Title, Text } = Typography

export default function HomePage() {
  const { styles } = useStyles()
  // const { t } = useTranslation()

  return (
    <>
      {/* Header avec logos */}
      <div className={styles.header}>
        <Space direction="vertical" align="center" size="large">
          <Image
            preview={false}
            src="/assets/douaneBeninLogo.png"
            alt="Logo douane"
            className={styles.mainLogo}
          />
          <Image
            preview={false}
            src="/assets/mefOfficialLogo.png"
            alt="Logo mef"
            width={200}
            className={styles.secondaryLogo}
          />
          <div className={styles.welcomeText}>
            <Title level={1} className={styles.welcomeTitle}>
              Bienvenue sur le portail <span className="whitespace-nowrap">e-service</span>
            </Title>
            <Text className={styles.welcomeSubtitle}>
              Accédez à tous nos services en un seul endroit
            </Text>
          </div>
        </Space>
      </div>

      {/* Grille des services avec barre de recherche */}
      <div className={styles.servicesSection}>
        <ServicesGrid />
      </div>
    </>
  )
}

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      backgroundColor: token.colorBgLayout,
      backgroundImage: 'url("/assets/homeBackground.png")',
      backgroundPosition: "center",
      backgroundSize: "cover",
      backgroundAttachment: "fixed",
      minHeight: "calc(100vh - var(--dgd-head-height))",
      width: "100%",
      display: "flex",
      flexDirection: "column",
    },
    header: {
      padding: "3rem 2rem 2rem",
      textAlign: "center",
      width: "100%",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      backgroundImage: 'url("/assets/amazoneBenin.jpg")',
      // backgroundPosition: "center",
      // backgroundSize: "cover",
      backgroundAttachment: "fixed",
      backgroundRepeat: "no-repeat",
      backgroundSize: "cover",
      backdropFilter: "blur(10px)",
      borderBottom: `1px solid ${token.colorBorder}`,
    },
    mainLogo: {
      maxHeight: "120px",
      width: "auto",
    },
    secondaryLogo: {
      maxHeight: "80px",
      width: "auto",
    },
    welcomeText: {
      marginTop: "1rem",
    },
    welcomeTitle: {
      color: `${token.colorPrimary} !important`,
      fontSize: "2.5rem !important",
      fontWeight: "700 !important",
      marginBottom: "0.5rem !important",
      textShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    },
    welcomeSubtitle: {
      color: token.colorText,
      fontSize: "1.2rem",
      fontWeight: "500",
      display: "block",
    },
    servicesSection: {
      flex: 1,
      backgroundColor: "rgba(255, 255, 255, 0.98)",
      backdropFilter: "blur(5px)",
      padding: "2rem 0",
      minHeight: "60vh",
    },
    footer: {
      textAlign: "center",
      color: "white",
      fontWeight: "bold",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      backdropFilter: "blur(10px)",
      borderTop: `1px solid ${token.colorBorder}`,
      padding: "1rem",
    },
  }
})
