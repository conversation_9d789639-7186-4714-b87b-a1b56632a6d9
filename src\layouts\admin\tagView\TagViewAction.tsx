import { SettingOutlined } from "@ant-design/icons"
import { Dropdown } from "antd"
import { useDispatch, useSelector } from "react-redux"
import { removeAllTag, removeOtherTag, removeTagByPath } from "~/redux/slices/tagSlice"
import { useTranslation } from "react-i18next"
import { RootState } from "~/redux/store"
import { PATH_ADMIN } from "~/constants"
import { useLocation, useNavigate } from "react-router"

function TagsViewAction() {
  const { activeTagId } = useSelector((state: RootState) => state.tag)
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation()
  const { pathname } = useLocation()

  const isHomeTagActive = activeTagId === PATH_ADMIN.root

  return (
    <Dropdown
      menu={{
        items: [
          {
            key: "0",
            onClick: () => {
              dispatch(removeOtherTag(undefined))
            },
            label: t("tagsView.closeOther"),
          },
          {
            key: "1",
            onClick: () => {
              dispatch(removeAllTag(undefined))
              navigate(PATH_ADMIN.root)
            },
            label: t("tagsView.closeAll"),
          },
        ],
      }}
    >
        <SettingOutlined className="mr-2" />
    </Dropdown>
  )
}

export default TagsViewAction
