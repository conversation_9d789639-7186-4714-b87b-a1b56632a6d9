import { <PERSON><PERSON>ontainer, ProCard } from "@ant-design/pro-components";
import { Button, Typography, Table, Space, Tag, Modal, message, Row, Col, Card } from "antd";
import {
  PlusOutlined,
  SettingOutlined,
  PercentageOutlined,
  EditOutlined,
  PlusCircleOutlined,
  CalculatorOutlined,
  FileTextOutlined,
  EyeOutlined
} from "@ant-design/icons";
import { useNavigate } from "react-router";
import { REPARTITIONS_PATH, useStyles } from ".";

const PrimesList = () => {
  const navigate = useNavigate();
  const { styles } = useStyles();

  const handleAllRepartitions= () => {
    navigate(REPARTITIONS_PATH);
    message.info("Navigation vers l'ajout de prime");
  };

  const handleAddRepartition = () => {
    navigate("/admin/primes/add/repartition");
  };

  // Gestionnaires d'événements pour les grids cliquables
  const handleAddPrime = () => {
    navigate("/admin/primes/add");
    message.info("Navigation vers l'ajout de prime");
  };

  const handleModifyPrime = () => {
    navigate("/admin/primes/modify");
    message.info("Navigation vers la modification de prime");
  };

  const handleAddCoefficient = () => {
    navigate("/admin/primes/coefficients/add");
    message.info("Navigation vers l'ajout de coefficient");
  };

  const handleModifyCoefficient = () => {
    navigate("/admin/primes/coefficients/modify");
    message.info("Navigation vers la modification de coefficient");
  };

  const handleViewReports = () => {
    navigate("/admin/primes/reports");
    message.info("Navigation vers les rapports");
  };

  const handleSettings = () => {
    navigate("/admin/primes/settings");
    message.info("Navigation vers les paramètres");
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/primes/edit/${id}`);
  };

  const handlePrimeManagement = () => {
    navigate("/admin/primes/management");
  };

  const handleTypePrimeManagement = () => {
    navigate("/admin/primes/typePrime");
  };

  const handleCoefficientManagement = () => {
    navigate("/admin/primes/coefficients");
  };

  return (
    <PageContainer
      content={
        <ProCard boxShadow bordered >
          <Card type="inner">
            <Card.Grid
              className={styles.gridStyle}
              onClick={handleAllRepartitions}
            >
              <div className={styles.gridContent}>
                <EyeOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Voir Répartitions
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Consulter les répartitions existantes
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleAddRepartition}
            >
              <div className={styles.gridContent}>
                <PlusCircleOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Ajouter Répartition
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Créer une nouvelle répartition
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleViewReports}
            >
              <div className={styles.gridContent}>
                <FileTextOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Rapports
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Consulter les rapports
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleModifyPrime}
            >
              <div className={styles.gridContent}>
                <EditOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Modifier Prime
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Éditer une prime existante
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleAddCoefficient}
            >
              <div className={styles.gridContent}>
                <CalculatorOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Ajouter Coefficient
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Créer un nouveau coefficient
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleModifyCoefficient}
            >
              <div className={styles.gridContent}>
                <PercentageOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Modifier Coefficient
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Éditer un coefficient existant
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleSettings}
            >
              <div className={styles.gridContent}>
                <SettingOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Paramètres
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Configuration du système
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handlePrimeManagement}
            >
              <div className={styles.gridContent}>
                <SettingOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Gérer les Primes
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Gérer les Primes
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleTypePrimeManagement}
            >
              <div className={styles.gridContent}>
                <SettingOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Gérer les Types de Prime
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Gérer les Types de Prime
                </Typography.Text>
              </div>
            </Card.Grid>

            <Card.Grid
              className={styles.gridStyle}
              onClick={handleCoefficientManagement}
            >
              <div className={styles.gridContent}>
                <SettingOutlined className={styles.gridIcon} />
                <Typography.Title level={5} className={styles.gridTitle}>
                  Gérer les Coefficients
                </Typography.Title>
                <Typography.Text className={styles.gridDescription}>
                  Gérer les Coefficients
                </Typography.Text>
              </div>
            </Card.Grid>
          </Card>
        </ProCard>
      }
    />
  );
};

export default PrimesList;
