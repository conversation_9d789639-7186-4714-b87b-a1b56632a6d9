import { PATH_ADMIN } from "~/constants";
import { createPath } from "~/services/utils";
import { createStyles } from "~/themes";

export const PRIMES = "primes"
export const REPARTITIONS = "repartitions"
// export const ADD = "add"
// export const MODIFY = "modify"
// export const COEFFICIENTS = "coefficients"
// export const MANAGEMENT = "management"
// export const TYPE_PRIME = "type-prime"
// export const REPORTS = "reports"
// export const SETTINGS = "settings"

export const PRIMES_PATH = createPath(PRIMES, PATH_ADMIN.dafl)
export const REPARTITIONS_PATH = createPath(REPARTITIONS, PRIMES_PATH)


// export const repartitionsListLoader = async (): Promise<TypePrime[]> => {
//   try {
//     const response = await getTypesPrimesApi();
//     return response.data as TypePrime[];
//   } catch (error) {
//     return AppError.message(error, "Erreur lors du chargement des types de primes") || [];
//   }
// };


export const useStyles = createStyles(({ token }) => ({
  gridStyle: {
    width: '25%',
    textAlign: 'center',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: token.colorPrimaryBg,
      borderColor: token.colorPrimary,
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    '&:active': {
      transform: 'translateY(0)',
    },
  },
  gridContent: {
    padding: '0.1rem',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '6px',
  },
  gridIcon: {
    fontSize: '32px',
    color: token.colorPrimary,
  },
  gridTitle: {
    fontSize: '16px',
    fontWeight: 600,
    color: token.colorText,
    margin: 0,
  },
  gridDescription: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    margin: 0,
    textAlign: 'center',
  },
}));
