 import {
  ArrowLeftOutlined,
  BankOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  TeamOutlined,
  UserOutlined
} from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  message,
  Row,
  Space,
  Statistic,
  Table,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import * as XLSX from 'xlsx';
import { Agent } from "~/models/Agent";
import { getAgents } from "~/services/api/agentService";

const { Panel } = Collapse;

interface RepartitionData {
  id: string;
  nom: string;
  prenom?: string;
  matricule?: string;
  grade?: string;
  fonction?: string;
  unite: string;
  coefficient: number;
  montant: number;
}

interface UniteRepartition {
  id: string;
  unite: string;
  nombreAgents: number;
  totalCoefficient: number;
  montantTotal: number;
}

interface FonctionRepartition {
  id: string;
  fonction: string;
  libelle: string;
  nombreAgents: number;
  totalCoefficient: number;
  montantTotal: number;
}

// const RepartitionsPage = () => {
//   const navigate = useNavigate();
//   const [loading, setLoading] = useState(false);
//   const [_agents, setAgents] = useState<Agent[]>([]);
//   const [repartitionAgents, setRepartitionAgents] = useState<RepartitionData[]>([]);
//   const [repartitionUnites, setRepartitionUnites] = useState<UniteRepartition[]>([]);
//   const [repartitionFonctions, setRepartitionFonctions] = useState<FonctionRepartition[]>([]);

//   // Montant total fictif pour les calculs
//   const MONTANT_TOTAL = 500000;

//   const fetchData = async () => {
//     setLoading(true);
//     try {
//       const response = await getAgents();
//       const agentsData = response.data;
//       setAgents(agentsData);

//       // Calculer les répartitions
//       calculateRepartitions(agentsData);
//     } catch (_error) {
//       message.error("Erreur lors du chargement des données");
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     fetchData();
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   const calculateRepartitions = (agentsData: Agent[]) => {
//     // Calcul du total des coefficients
//     const totalCoefficient = agentsData.reduce((sum, agent) => sum + agent.coefficient, 0);

//     // Répartition par agent
//     const agentRepartitions: RepartitionData[] = agentsData.map(agent => ({
//       id: agent.id,
//       nom: agent.nom,
//       prenom: agent.prenom,
//       matricule: agent.matricule,
//       grade: agent.grade,
//       fonction: agent.fonction,
//       unite: agent.unite,
//       coefficient: agent.coefficient,
//       montant: (agent.coefficient / totalCoefficient) * MONTANT_TOTAL
//     }));
//     setRepartitionAgents(agentRepartitions);

//     // Répartition par unité
//     const unitesMap = new Map<string, UniteRepartition>();
//     agentsData.forEach(agent => {
//       const unite = agent.unite;
//       if (unitesMap.has(unite)) {
//         const existing = unitesMap.get(unite)!;
//         existing.nombreAgents += 1;
//         existing.totalCoefficient += agent.coefficient;
//         existing.montantTotal += (agent.coefficient / totalCoefficient) * MONTANT_TOTAL;
//       } else {
//         unitesMap.set(unite, {
//           id: unite,
//           unite,
//           nombreAgents: 1,
//           totalCoefficient: agent.coefficient,
//           montantTotal: (agent.coefficient / totalCoefficient) * MONTANT_TOTAL
//         });
//       }
//     });
//     setRepartitionUnites(Array.from(unitesMap.values()));

//     // Répartition par fonction
//     const fonctionsMap = new Map<string, FonctionRepartition>();
//     const fonctionLabels: { [key: string]: string } = {
//       'chef_service': 'Chef de service',
//       'responsable_unite': 'Responsable d\'unité',
//       'agent_terrain': 'Agent de terrain',
//       'verificateur': 'Vérificateur',
//       'directeur_adjoint': 'Directeur adjoint',
//       'controleur': 'Contrôleur',
//       'inspecteur': 'Inspecteur',
//       'secretaire': 'Secrétaire',
//       'chauffeur': 'Chauffeur'
//     };

//     agentsData.forEach(agent => {
//       const fonction = agent.fonction;
//       if (fonctionsMap.has(fonction)) {
//         const existing = fonctionsMap.get(fonction)!;
//         existing.nombreAgents += 1;
//         existing.totalCoefficient += agent.coefficient;
//         existing.montantTotal += (agent.coefficient / totalCoefficient) * MONTANT_TOTAL;
//       } else {
//         fonctionsMap.set(fonction, {
//           id: fonction,
//           fonction,
//           libelle: fonctionLabels[fonction] || fonction,
//           nombreAgents: 1,
//           totalCoefficient: agent.coefficient,
//           montantTotal: (agent.coefficient / totalCoefficient) * MONTANT_TOTAL
//         });
//       }
//     });
//     setRepartitionFonctions(Array.from(fonctionsMap.values()));
//   };

//   const handleBack = () => {
//     navigate("/primes");
//   };

//   // Fonctions d'export Excel
//   const exportToExcel = (data: any[], filename: string, sheetName: string) => {
//     const worksheet = XLSX.utils.json_to_sheet(data);
//     const workbook = XLSX.utils.book_new();
//     XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
//     XLSX.writeFile(workbook, `${filename}.xlsx`);
//     message.success(`Export réussi : ${filename}.xlsx`);
//   };

//   const exportAgentsToExcel = () => {
//     const data = repartitionAgents.map(agent => ({
//       'Matricule': agent.matricule,
//       'Nom': agent.nom,
//       'Prénom': agent.prenom,
//       'Grade': agent.grade,
//       'Fonction': agent.fonction,
//       'Unité': agent.unite,
//       'Coefficient': agent.coefficient,
//       'Montant (€)': Math.round(agent.montant)
//     }));
//     exportToExcel(data, 'repartition-agents', 'Répartition par Agent');
//   };

//   const exportUnitesToExcel = () => {
//     const data = repartitionUnites.map(unite => ({
//       'Unité': unite.unite,
//       'Nombre d\'agents': unite.nombreAgents,
//       'Total coefficient': unite.totalCoefficient.toFixed(2),
//       'Montant total (€)': Math.round(unite.montantTotal)
//     }));
//     exportToExcel(data, 'repartition-unites', 'Répartition par Unité');
//   };

//   const exportFonctionsToExcel = () => {
//     const data = repartitionFonctions.map(fonction => ({
//       'Fonction': fonction.libelle,
//       'Nombre d\'agents': fonction.nombreAgents,
//       'Total coefficient': fonction.totalCoefficient.toFixed(2),
//       'Montant total (€)': Math.round(fonction.montantTotal)
//     }));
//     exportToExcel(data, 'repartition-fonctions', 'Répartition par Fonction');
//   };

//   const exportAllToExcel = () => {
//     const workbook = XLSX.utils.book_new();

//     // Feuille 1: Répartition par Agent
//     const agentsData = repartitionAgents.map(agent => ({
//       'Matricule': agent.matricule,
//       'Nom': agent.nom,
//       'Prénom': agent.prenom,
//       'Grade': agent.grade,
//       'Fonction': agent.fonction,
//       'Unité': agent.unite,
//       'Coefficient': agent.coefficient,
//       'Montant (€)': Math.round(agent.montant)
//     }));
//     const agentsSheet = XLSX.utils.json_to_sheet(agentsData);
//     XLSX.utils.book_append_sheet(workbook, agentsSheet, 'Agents');

//     // Feuille 2: Répartition par Unité
//     const unitesData = repartitionUnites.map(unite => ({
//       'Unité': unite.unite,
//       'Nombre d\'agents': unite.nombreAgents,
//       'Total coefficient': unite.totalCoefficient.toFixed(2),
//       'Montant total (€)': Math.round(unite.montantTotal)
//     }));
//     const unitesSheet = XLSX.utils.json_to_sheet(unitesData);
//     XLSX.utils.book_append_sheet(workbook, unitesSheet, 'Unités');

//     // Feuille 3: Répartition par Fonction
//     const fonctionsData = repartitionFonctions.map(fonction => ({
//       'Fonction': fonction.libelle,
//       'Nombre d\'agents': fonction.nombreAgents,
//       'Total coefficient': fonction.totalCoefficient.toFixed(2),
//       'Montant total (€)': Math.round(fonction.montantTotal)
//     }));
//     const fonctionsSheet = XLSX.utils.json_to_sheet(fonctionsData);
//     XLSX.utils.book_append_sheet(workbook, fonctionsSheet, 'Fonctions');

//     XLSX.writeFile(workbook, 'repartitions-completes.xlsx');
//     message.success('Export complet réussi : repartitions-completes.xlsx');
//   };

//   // Définition des colonnes pour les tableaux
//   const agentsColumns = [
//     {
//       title: 'Matricule',
//       dataIndex: 'matricule',
//       key: 'matricule',
//       sorter: (a: RepartitionData, b: RepartitionData) => a.matricule!.localeCompare(b.matricule!),
//     },
//     {
//       title: 'Nom',
//       dataIndex: 'nom',
//       key: 'nom',
//       sorter: (a: RepartitionData, b: RepartitionData) => a.nom.localeCompare(b.nom),
//     },
//     {
//       title: 'Prénom',
//       dataIndex: 'prenom',
//       key: 'prenom',
//       sorter: (a: RepartitionData, b: RepartitionData) => a.prenom!.localeCompare(b.prenom!),
//     },
//     {
//       title: 'Grade',
//       dataIndex: 'grade',
//       key: 'grade',
//     },
//     {
//       title: 'Fonction',
//       dataIndex: 'fonction',
//       key: 'fonction',
//     },
//     {
//       title: 'Unité',
//       dataIndex: 'unite',
//       key: 'unite',
//     },
//     {
//       title: 'Coefficient',
//       dataIndex: 'coefficient',
//       key: 'coefficient',
//       sorter: (a: RepartitionData, b: RepartitionData) => a.coefficient - b.coefficient,
//       render: (value: number) => value.toFixed(2),
//     },
//     {
//       title: 'Montant (€)',
//       dataIndex: 'montant',
//       key: 'montant',
//       sorter: (a: RepartitionData, b: RepartitionData) => a.montant - b.montant,
//       render: (value: number) => Math.round(value).toLocaleString(),
//     },
//   ];

//   const unitesColumns = [
//     {
//       title: 'Unité',
//       dataIndex: 'unite',
//       key: 'unite',
//       sorter: (a: UniteRepartition, b: UniteRepartition) => a.unite.localeCompare(b.unite),
//     },
//     {
//       title: 'Nombre d\'agents',
//       dataIndex: 'nombreAgents',
//       key: 'nombreAgents',
//       sorter: (a: UniteRepartition, b: UniteRepartition) => a.nombreAgents - b.nombreAgents,
//     },
//     {
//       title: 'Total coefficient',
//       dataIndex: 'totalCoefficient',
//       key: 'totalCoefficient',
//       sorter: (a: UniteRepartition, b: UniteRepartition) => a.totalCoefficient - b.totalCoefficient,
//       render: (value: number) => value.toFixed(2),
//     },
//     {
//       title: 'Montant total (€)',
//       dataIndex: 'montantTotal',
//       key: 'montantTotal',
//       sorter: (a: UniteRepartition, b: UniteRepartition) => a.montantTotal - b.montantTotal,
//       render: (value: number) => Math.round(value).toLocaleString(),
//     },
//   ];

//   const fonctionsColumns = [
//     {
//       title: 'Fonction',
//       dataIndex: 'libelle',
//       key: 'libelle',
//       sorter: (a: FonctionRepartition, b: FonctionRepartition) => a.libelle.localeCompare(b.libelle),
//     },
//     {
//       title: 'Nombre d\'agents',
//       dataIndex: 'nombreAgents',
//       key: 'nombreAgents',
//       sorter: (a: FonctionRepartition, b: FonctionRepartition) => a.nombreAgents - b.nombreAgents,
//     },
//     {
//       title: 'Total coefficient',
//       dataIndex: 'totalCoefficient',
//       key: 'totalCoefficient',
//       sorter: (a: FonctionRepartition, b: FonctionRepartition) => a.totalCoefficient - b.totalCoefficient,
//       render: (value: number) => value.toFixed(2),
//     },
//     {
//       title: 'Montant total (€)',
//       dataIndex: 'montantTotal',
//       key: 'montantTotal',
//       sorter: (a: FonctionRepartition, b: FonctionRepartition) => a.montantTotal - b.montantTotal,
//       render: (value: number) => Math.round(value).toLocaleString(),
//     },
//   ];

//   // Calcul des totaux pour l'affichage
//   const totalCoefficient = repartitionAgents.reduce((sum, agent) => sum + agent.coefficient, 0);
//   const totalMontant = repartitionAgents.reduce((sum, agent) => sum + agent.montant, 0);

//   return (
//     <PageContainer
//       loading={loading}
//       header={{
//         title: (
//           <Typography.Title level={3} className="w-full">
//             Répartitions des Primes
//           </Typography.Title>
//         ),
//         onBack: handleBack,
//         backIcon: <ArrowLeftOutlined />,
//         extra: [
//           <Button
//             key="export-all"
//             type="primary"
//             icon={<FileExcelOutlined />}
//             onClick={exportAllToExcel}
//             size="large"
//           >
//             Télécharger tout (Excel)
//           </Button>
//         ]
//       }}
//     >
//       {/* Statistiques générales */}
//       <Card style={{ marginBottom: 24 }}>
//         <Row gutter={16}>
//           <Col span={6}>
//             <Statistic
//               title="Nombre total d'agents"
//               value={repartitionAgents.length}
//               prefix={<TeamOutlined />}
//             />
//           </Col>
//           <Col span={6}>
//             <Statistic
//               title="Total des coefficients"
//               value={totalCoefficient.toFixed(2)}
//             />
//           </Col>
//           <Col span={6}>
//             <Statistic
//               title="Montant total à répartir"
//               value={MONTANT_TOTAL}
//               suffix="€"
//               formatter={(value) => `${Number(value).toLocaleString()}`}
//             />
//           </Col>
//           <Col span={6}>
//             <Statistic
//               title="Montant calculé"
//               value={Math.round(totalMontant)}
//               suffix="€"
//               formatter={(value) => `${Number(value).toLocaleString()}`}
//             />
//           </Col>
//         </Row>
//       </Card>

//       {/* Tableaux dans des cartes pliables */}
//       <Collapse
//         defaultActiveKey={['1']}
//         size="large"
//         style={{ marginBottom: 24 }}
//       >
//         {/* Tableau 1: Répartition par Agent */}
//         <Panel
//           header={
//             <Space>
//               <UserOutlined />
//               <span>Répartition par Agent ({repartitionAgents.length} agents)</span>
//             </Space>
//           }
//           key="1"
//           extra={
//             <Button
//               type="link"
//               icon={<DownloadOutlined />}
//               onClick={(e) => {
//                 e.stopPropagation();
//                 exportAgentsToExcel();
//               }}
//             >
//               Excel
//             </Button>
//           }
//         >
//           <Table
//             dataSource={repartitionAgents}
//             columns={agentsColumns}
//             rowKey="id"
//             pagination={{ pageSize: 10, showSizeChanger: true }}
//             scroll={{ x: 800 }}
//             size="small"
//           />
//         </Panel>

//         {/* Tableau 2: Répartition par Unité */}
//         <Panel
//           header={
//             <Space>
//               <BankOutlined />
//               <span>Répartition par Unité ({repartitionUnites.length} unités)</span>
//             </Space>
//           }
//           key="2"
//           extra={
//             <Button
//               type="link"
//               icon={<DownloadOutlined />}
//               onClick={(e) => {
//                 e.stopPropagation();
//                 exportUnitesToExcel();
//               }}
//             >
//               Excel
//             </Button>
//           }
//         >
//           <Table
//             dataSource={repartitionUnites}
//             columns={unitesColumns}
//             rowKey="id"
//             pagination={{ pageSize: 10, showSizeChanger: true }}
//             size="small"
//           />
//         </Panel>

//         {/* Tableau 3: Répartition par Fonction */}
//         <Panel
//           header={
//             <Space>
//               <TeamOutlined />
//               <span>Répartition par Fonction ({repartitionFonctions.length} fonctions)</span>
//             </Space>
//           }
//           key="3"
//           extra={
//             <Button
//               type="link"
//               icon={<DownloadOutlined />}
//               onClick={(e) => {
//                 e.stopPropagation();
//                 exportFonctionsToExcel();
//               }}
//             >
//               Excel
//             </Button>
//           }
//         >
//           <Table
//             dataSource={repartitionFonctions}
//             columns={fonctionsColumns}
//             rowKey="id"
//             pagination={{ pageSize: 10, showSizeChanger: true }}
//             size="small"
//           />
//         </Panel>
//       </Collapse>
//     </PageContainer>
//   );
// };

const RepartitionsPage = () => {
  useEffect(() => {
    console.log("helo word !");

  }, [])

  return (
    <PageContainer
      header={{
        title: (
          <Typography.Title level={3} className="w-full">
            Répartitions des Primes
          </Typography.Title>
        ),
        // onBack: handleBack,
        backIcon: <ArrowLeftOutlined />,
      }}
    >
      <Card>
        <Typography.Text>
          En cours de développement
        </Typography.Text>
      </Card>
    </PageContainer>
  );
};

export default RepartitionsPage;
